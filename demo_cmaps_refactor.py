#!/usr/bin/env python3
"""
Demonstration of the refactored colormap dynamic loading functionality.

This script shows how to use the new dynamic loading system:
1. Direct access to colormaps (backward compatible)
2. Access through the unified cmaps interface
3. Access through specific collections
4. Performance benefits of lazy loading
"""

import sys
from pathlib import Path
import time

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_basic_usage():
    """Demonstrate basic usage - backward compatible."""
    print("=== Basic Usage (Backward Compatible) ===")
    
    import faninsar.cmaps as cmaps
    
    # Access colormaps directly as before
    print("Accessing colormaps directly:")
    gmt_cmap = cmaps.abyss
    print(f"  GMT abyss: {type(gmt_cmap).__name__}")
    
    scm_cmap = cmaps.acton
    print(f"  SCM acton: {type(scm_cmap).__name__}")
    
    cmocean_cmap = cmaps.algae
    print(f"  cmocean algae: {type(cmocean_cmap).__name__}")
    
    # Reversed colormaps work too
    reversed_cmap = cmaps.abyss_r
    print(f"  GMT abyss_r: {type(reversed_cmap).__name__}")
    
    # Custom colormaps still work
    custom_cmap = cmaps.RdGyBu
    print(f"  Custom RdGyBu: {type(custom_cmap).__name__}")
    
    print()


def demo_unified_interface():
    """Demonstrate the unified cmaps interface."""
    print("=== Unified Cmaps Interface ===")
    
    import faninsar.cmaps as cmaps
    
    # Access through the unified interface
    print("Accessing through cmaps instance:")
    
    # Direct access to any colormap
    any_cmap = cmaps.cmaps.abyss
    print(f"  Direct access: {type(any_cmap).__name__}")
    
    # Access through specific collections
    gmt_cmap = cmaps.cmaps.GMT.abyss
    print(f"  Through GMT collection: {type(gmt_cmap).__name__}")
    
    scm_cmap = cmaps.cmaps.SCM.acton
    print(f"  Through SCM collection: {type(scm_cmap).__name__}")
    
    cmocean_cmap = cmaps.cmaps.cmocean.algae
    print(f"  Through cmocean collection: {type(cmocean_cmap).__name__}")
    
    colorcet_cmap = cmaps.cmaps.colorcet.bkr
    print(f"  Through colorcet collection: {type(colorcet_cmap).__name__}")
    
    mintpy_cmap = cmaps.cmaps.mintpy.cmy
    print(f"  Through mintpy collection: {type(mintpy_cmap).__name__}")
    
    print()


def demo_lazy_loading():
    """Demonstrate lazy loading performance."""
    print("=== Lazy Loading Performance ===")
    
    import faninsar.cmaps as cmaps
    
    print("Testing lazy loading performance:")
    
    # First access - loads from file
    start_time = time.time()
    cmap1 = cmaps.relief  # A GMT colormap
    first_time = time.time() - start_time
    print(f"  First access (file load): {first_time:.6f} seconds")
    
    # Second access - uses cache
    start_time = time.time()
    cmap2 = cmaps.relief
    second_time = time.time() - start_time
    print(f"  Second access (cached): {second_time:.6f} seconds")
    
    # Verify it's the same object
    print(f"  Same object cached: {cmap1 is cmap2}")
    
    # Show that only accessed colormaps are loaded
    print(f"  Colormap type: {type(cmap1).__name__}")
    
    print()


def demo_collections_info():
    """Show information about available collections."""
    print("=== Available Collections ===")
    
    import faninsar.cmaps as cmaps
    
    collections = ['GMT', 'SCM', 'cmocean', 'colorcet', 'mintpy']
    
    for collection_name in collections:
        collection = getattr(cmaps.cmaps, collection_name)
        colormap_count = len(collection.colormap_names)
        total_count = len(collection.__all__)  # includes reversed versions
        
        print(f"{collection_name}:")
        print(f"  Base colormaps: {colormap_count}")
        print(f"  Total (with reversed): {total_count}")
        print(f"  Examples: {', '.join(collection.colormap_names[:3])}")
        print()


def demo_error_handling():
    """Demonstrate error handling."""
    print("=== Error Handling ===")
    
    import faninsar.cmaps as cmaps
    
    print("Testing error handling:")
    
    # Try to access non-existent colormap
    try:
        _ = cmaps.nonexistent_colormap
        print("  ✗ Should have raised AttributeError")
    except AttributeError as e:
        print(f"  ✓ Correctly caught error: {e}")
    
    # Try through cmaps interface
    try:
        _ = cmaps.cmaps.another_nonexistent_colormap
        print("  ✗ Should have raised AttributeError")
    except AttributeError as e:
        print(f"  ✓ Correctly caught error: {e}")
    
    print()


def main():
    """Run all demonstrations."""
    print("FanInSAR Colormap Dynamic Loading Demonstration")
    print("=" * 50)
    print()
    
    demo_basic_usage()
    demo_unified_interface()
    demo_lazy_loading()
    demo_collections_info()
    demo_error_handling()
    
    print("=" * 50)
    print("Demonstration complete!")
    print()
    print("Key benefits of the refactored system:")
    print("• Dynamic loading - colormaps loaded only when accessed")
    print("• Caching - subsequent accesses use cached objects")
    print("• Unified interface - access all colormaps through cmaps instance")
    print("• Backward compatibility - existing code continues to work")
    print("• Better organization - clear separation of colormap collections")


if __name__ == "__main__":
    main()
