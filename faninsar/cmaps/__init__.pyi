"""Type stubs for faninsar.cmaps module to improve code completion."""

from pathlib import Path

import matplotlib.colors as mcolors

from .enhanced_colormap import EnhancedLinearSegmentedColormap

class ColormapLoader:
    """Base class for colormap loaders."""

    data_dir: Path
    colormap_names: list[str]

    def __init__(self, data_dir: str | Path) -> None: ...
    def __getattr__(self, name: str) -> EnhancedLinearSegmentedColormap: ...
    def __dir__(self) -> list[str]: ...
    @property
    def __all__(self) -> list[str]: ...

class GMTColormaps(ColormapLoader):
    """GMT colormap loader."""

    # GMT colormaps
    abyss: mcolors.LinearSegmentedColormap
    abyss_r: mcolors.LinearSegmentedColormap
    bathy: mcolors.LinearSegmentedColormap
    bathy_r: mcolors.LinearSegmentedColormap
    cool: mcolors.LinearSegmentedColormap
    cool_r: mcolors.LinearSegmentedColormap
    copper: mcolors.LinearSegmentedColormap
    copper_r: mcolors.LinearSegmentedColormap
    cubhelix: mcolors.LinearSegmentedColormap
    cubhelix_r: mcolors.LinearSegmentedColormap
    cyclic: mcolors.LinearSegmentedColormap
    cyclic_r: mcolors.LinearSegmentedColormap
    dem1: mcolors.LinearSegmentedColormap
    dem1_r: mcolors.LinearSegmentedColormap
    dem2: mcolors.LinearSegmentedColormap
    dem2_r: mcolors.LinearSegmentedColormap
    dem3: mcolors.LinearSegmentedColormap
    dem3_r: mcolors.LinearSegmentedColormap
    dem4: mcolors.LinearSegmentedColormap
    dem4_r: mcolors.LinearSegmentedColormap
    drywet: mcolors.LinearSegmentedColormap
    drywet_r: mcolors.LinearSegmentedColormap
    earth: mcolors.LinearSegmentedColormap
    earth_r: mcolors.LinearSegmentedColormap
    elevation: mcolors.LinearSegmentedColormap
    elevation_r: mcolors.LinearSegmentedColormap
    etopo1: mcolors.LinearSegmentedColormap
    etopo1_r: mcolors.LinearSegmentedColormap
    geo: mcolors.LinearSegmentedColormap
    geo_r: mcolors.LinearSegmentedColormap
    globe: mcolors.LinearSegmentedColormap
    globe_r: mcolors.LinearSegmentedColormap
    gray: mcolors.LinearSegmentedColormap
    gray_r: mcolors.LinearSegmentedColormap
    haxby: mcolors.LinearSegmentedColormap
    haxby_r: mcolors.LinearSegmentedColormap
    hot: mcolors.LinearSegmentedColormap
    hot_r: mcolors.LinearSegmentedColormap
    inferno: mcolors.LinearSegmentedColormap
    inferno_r: mcolors.LinearSegmentedColormap
    jet: mcolors.LinearSegmentedColormap
    jet_r: mcolors.LinearSegmentedColormap
    magma: mcolors.LinearSegmentedColormap
    magma_r: mcolors.LinearSegmentedColormap
    nighttime: mcolors.LinearSegmentedColormap
    nighttime_r: mcolors.LinearSegmentedColormap
    no_green: mcolors.LinearSegmentedColormap
    no_green_r: mcolors.LinearSegmentedColormap
    ocean: mcolors.LinearSegmentedColormap
    ocean_r: mcolors.LinearSegmentedColormap
    plasma: mcolors.LinearSegmentedColormap
    plasma_r: mcolors.LinearSegmentedColormap
    polar: mcolors.LinearSegmentedColormap
    polar_r: mcolors.LinearSegmentedColormap
    rainbow: mcolors.LinearSegmentedColormap
    rainbow_r: mcolors.LinearSegmentedColormap
    red2green: mcolors.LinearSegmentedColormap
    red2green_r: mcolors.LinearSegmentedColormap
    relief: mcolors.LinearSegmentedColormap
    relief_r: mcolors.LinearSegmentedColormap
    seafloor: mcolors.LinearSegmentedColormap
    seafloor_r: mcolors.LinearSegmentedColormap
    sealand: mcolors.LinearSegmentedColormap
    sealand_r: mcolors.LinearSegmentedColormap
    seis: mcolors.LinearSegmentedColormap
    seis_r: mcolors.LinearSegmentedColormap
    split: mcolors.LinearSegmentedColormap
    split_r: mcolors.LinearSegmentedColormap
    srtm: mcolors.LinearSegmentedColormap
    srtm_r: mcolors.LinearSegmentedColormap
    terra: mcolors.LinearSegmentedColormap
    terra_r: mcolors.LinearSegmentedColormap
    topo: mcolors.LinearSegmentedColormap
    topo_r: mcolors.LinearSegmentedColormap
    turbo: mcolors.LinearSegmentedColormap
    turbo_r: mcolors.LinearSegmentedColormap
    viridis: mcolors.LinearSegmentedColormap
    viridis_r: mcolors.LinearSegmentedColormap
    world: mcolors.LinearSegmentedColormap
    world_r: mcolors.LinearSegmentedColormap
    wysiwyg: mcolors.LinearSegmentedColormap
    wysiwyg_r: mcolors.LinearSegmentedColormap

class SCMColormaps(ColormapLoader):
    """SCM colormap loader."""

    version: str
    # SCM colormaps
    acton: mcolors.LinearSegmentedColormap
    acton_r: mcolors.LinearSegmentedColormap
    bam: mcolors.LinearSegmentedColormap
    bam_r: mcolors.LinearSegmentedColormap
    bamako: mcolors.LinearSegmentedColormap
    bamako_r: mcolors.LinearSegmentedColormap
    bamO: mcolors.LinearSegmentedColormap
    bamO_r: mcolors.LinearSegmentedColormap
    batlow: mcolors.LinearSegmentedColormap
    batlow_r: mcolors.LinearSegmentedColormap
    batlowK: mcolors.LinearSegmentedColormap
    batlowK_r: mcolors.LinearSegmentedColormap
    batlowW: mcolors.LinearSegmentedColormap
    batlowW_r: mcolors.LinearSegmentedColormap
    berlin: mcolors.LinearSegmentedColormap
    berlin_r: mcolors.LinearSegmentedColormap
    bilbao: mcolors.LinearSegmentedColormap
    bilbao_r: mcolors.LinearSegmentedColormap
    broc: mcolors.LinearSegmentedColormap
    broc_r: mcolors.LinearSegmentedColormap
    brocO: mcolors.LinearSegmentedColormap
    brocO_r: mcolors.LinearSegmentedColormap
    buda: mcolors.LinearSegmentedColormap
    buda_r: mcolors.LinearSegmentedColormap
    bukavu: mcolors.LinearSegmentedColormap
    bukavu_r: mcolors.LinearSegmentedColormap
    cork: mcolors.LinearSegmentedColormap
    cork_r: mcolors.LinearSegmentedColormap
    corkO: mcolors.LinearSegmentedColormap
    corkO_r: mcolors.LinearSegmentedColormap
    davos: mcolors.LinearSegmentedColormap
    davos_r: mcolors.LinearSegmentedColormap
    devon: mcolors.LinearSegmentedColormap
    devon_r: mcolors.LinearSegmentedColormap
    fes: mcolors.LinearSegmentedColormap
    fes_r: mcolors.LinearSegmentedColormap
    glasgow: mcolors.LinearSegmentedColormap
    glasgow_r: mcolors.LinearSegmentedColormap
    grayC: mcolors.LinearSegmentedColormap
    grayC_r: mcolors.LinearSegmentedColormap
    hawaii: mcolors.LinearSegmentedColormap
    hawaii_r: mcolors.LinearSegmentedColormap
    imola: mcolors.LinearSegmentedColormap
    imola_r: mcolors.LinearSegmentedColormap
    lajolla: mcolors.LinearSegmentedColormap
    lajolla_r: mcolors.LinearSegmentedColormap
    lapaz: mcolors.LinearSegmentedColormap
    lapaz_r: mcolors.LinearSegmentedColormap
    lipari: mcolors.LinearSegmentedColormap
    lipari_r: mcolors.LinearSegmentedColormap
    lisbon: mcolors.LinearSegmentedColormap
    lisbon_r: mcolors.LinearSegmentedColormap
    managua: mcolors.LinearSegmentedColormap
    managua_r: mcolors.LinearSegmentedColormap
    navia: mcolors.LinearSegmentedColormap
    navia_r: mcolors.LinearSegmentedColormap
    nuuk: mcolors.LinearSegmentedColormap
    nuuk_r: mcolors.LinearSegmentedColormap
    oleron: mcolors.LinearSegmentedColormap
    oleron_r: mcolors.LinearSegmentedColormap
    oslo: mcolors.LinearSegmentedColormap
    oslo_r: mcolors.LinearSegmentedColormap
    roma: mcolors.LinearSegmentedColormap
    roma_r: mcolors.LinearSegmentedColormap
    romaO: mcolors.LinearSegmentedColormap
    romaO_r: mcolors.LinearSegmentedColormap
    tofino: mcolors.LinearSegmentedColormap
    tofino_r: mcolors.LinearSegmentedColormap
    tokyo: mcolors.LinearSegmentedColormap
    tokyo_r: mcolors.LinearSegmentedColormap
    turku: mcolors.LinearSegmentedColormap
    turku_r: mcolors.LinearSegmentedColormap
    vanimo: mcolors.LinearSegmentedColormap
    vanimo_r: mcolors.LinearSegmentedColormap
    vik: mcolors.LinearSegmentedColormap
    vik_r: mcolors.LinearSegmentedColormap
    vikO: mcolors.LinearSegmentedColormap
    vikO_r: mcolors.LinearSegmentedColormap

class CmoceanColormaps(ColormapLoader):
    """Cmocean colormap loader."""

    # Cmocean colormaps
    algae: mcolors.LinearSegmentedColormap
    algae_r: mcolors.LinearSegmentedColormap
    amp: mcolors.LinearSegmentedColormap
    amp_r: mcolors.LinearSegmentedColormap
    balance: mcolors.LinearSegmentedColormap
    balance_r: mcolors.LinearSegmentedColormap
    curl: mcolors.LinearSegmentedColormap
    curl_r: mcolors.LinearSegmentedColormap
    deep: mcolors.LinearSegmentedColormap
    deep_r: mcolors.LinearSegmentedColormap
    delta: mcolors.LinearSegmentedColormap
    delta_r: mcolors.LinearSegmentedColormap
    dense: mcolors.LinearSegmentedColormap
    dense_r: mcolors.LinearSegmentedColormap
    diff: mcolors.LinearSegmentedColormap
    diff_r: mcolors.LinearSegmentedColormap
    gray: mcolors.LinearSegmentedColormap
    gray_r: mcolors.LinearSegmentedColormap
    haline: mcolors.LinearSegmentedColormap
    haline_r: mcolors.LinearSegmentedColormap
    ice: mcolors.LinearSegmentedColormap
    ice_r: mcolors.LinearSegmentedColormap
    matter: mcolors.LinearSegmentedColormap
    matter_r: mcolors.LinearSegmentedColormap
    oxy: mcolors.LinearSegmentedColormap
    oxy_r: mcolors.LinearSegmentedColormap
    phase: mcolors.LinearSegmentedColormap
    phase_r: mcolors.LinearSegmentedColormap
    rain: mcolors.LinearSegmentedColormap
    rain_r: mcolors.LinearSegmentedColormap
    solar: mcolors.LinearSegmentedColormap
    solar_r: mcolors.LinearSegmentedColormap
    speed: mcolors.LinearSegmentedColormap
    speed_r: mcolors.LinearSegmentedColormap
    tarn: mcolors.LinearSegmentedColormap
    tarn_r: mcolors.LinearSegmentedColormap
    tempo: mcolors.LinearSegmentedColormap
    tempo_r: mcolors.LinearSegmentedColormap
    thermal: mcolors.LinearSegmentedColormap
    thermal_r: mcolors.LinearSegmentedColormap
    topo: mcolors.LinearSegmentedColormap
    topo_r: mcolors.LinearSegmentedColormap
    turbid: mcolors.LinearSegmentedColormap
    turbid_r: mcolors.LinearSegmentedColormap

class ColorcetColormaps(ColormapLoader):
    """Colorcet colormap loader."""

    # Colorcet colormaps (partial list)
    bkr: mcolors.LinearSegmentedColormap
    bkr_r: mcolors.LinearSegmentedColormap
    bky: mcolors.LinearSegmentedColormap
    bky_r: mcolors.LinearSegmentedColormap
    bwy: mcolors.LinearSegmentedColormap
    bwy_r: mcolors.LinearSegmentedColormap
    colorwheel: mcolors.LinearSegmentedColormap
    colorwheel_r: mcolors.LinearSegmentedColormap
    coolwarm: mcolors.LinearSegmentedColormap
    coolwarm_r: mcolors.LinearSegmentedColormap
    fire: mcolors.LinearSegmentedColormap
    fire_r: mcolors.LinearSegmentedColormap
    rainbow: mcolors.LinearSegmentedColormap
    rainbow_r: mcolors.LinearSegmentedColormap

class MintpyColormaps(ColormapLoader):
    """Mintpy colormap loader."""

    # Mintpy colormaps
    cmy: mcolors.LinearSegmentedColormap
    cmy_r: mcolors.LinearSegmentedColormap
    dismph: mcolors.LinearSegmentedColormap
    dismph_r: mcolors.LinearSegmentedColormap
    romanian: mcolors.LinearSegmentedColormap
    romanian_r: mcolors.LinearSegmentedColormap

class Cmaps:
    """Unified colormap interface."""

    GMT: GMTColormaps
    SCM: SCMColormaps
    cmocean: CmoceanColormaps
    colorcet: ColorcetColormaps
    mintpy: MintpyColormaps

    def __getattr__(self, name: str) -> mcolors.LinearSegmentedColormap: ...
    def __dir__(self) -> list[str]: ...
    @property
    def __all__(self) -> list[str]: ...

# Global instance
cmaps: Cmaps

# Custom colormaps
GnBu_RdPl: mcolors.LinearSegmentedColormap
GnBu_RdPl_r: mcolors.LinearSegmentedColormap
RdGyBu: mcolors.LinearSegmentedColormap
RdGyBu_r: mcolors.LinearSegmentedColormap
WtBuPl: mcolors.LinearSegmentedColormap
WtBuPl_r: mcolors.LinearSegmentedColormap
WtBuGn: mcolors.LinearSegmentedColormap
WtBuGn_r: mcolors.LinearSegmentedColormap
WtRdPl: mcolors.LinearSegmentedColormap
WtRdPl_r: mcolors.LinearSegmentedColormap
WtHeatRed: mcolors.LinearSegmentedColormap
WtHeatRed_r: mcolors.LinearSegmentedColormap

# Module-level colormap access (all colormaps available at module level)
def __getattr__(name: str) -> mcolors.LinearSegmentedColormap: ...
