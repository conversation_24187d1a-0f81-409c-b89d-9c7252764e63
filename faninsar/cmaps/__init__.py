from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Union

import matplotlib.colors as mcolors
import numpy as np

# TODO: Add cpt-city colormaps


class ColormapLoader(ABC):
    """Abstract base class for dynamic colormap loading.

    This class provides the core functionality for loading LinearSegmentedColormap
    objects on-demand from data files, with caching to avoid repeated file I/O.
    """

    def __init__(self, data_dir: Union[str, Path]):
        """Initialize the colormap loader.

        Args:
            data_dir: Directory containing colormap data files

        """
        self.data_dir = Path(data_dir)
        self._cache: Dict[str, mcolors.LinearSegmentedColormap] = {}
        self._names: Optional[List[str]] = None

    @property
    @abstractmethod
    def colormap_names(self) -> List[str]:
        """Return list of available colormap names."""

    @abstractmethod
    def _load_colormap_data(self, name: str) -> np.ndarray:
        """Load colormap data from file.

        Args:
            name: Name of the colormap

        Returns:
            Numpy array containing colormap data

        """

    def _create_colormap(
        self, name: str, data: np.ndarray
    ) -> mcolors.LinearSegmentedColormap:
        """Create a LinearSegmentedColormap from data.

        Args:
            name: Name of the colormap
            data: Colormap data array

        Returns:
            LinearSegmentedColormap object

        """
        return mcolors.LinearSegmentedColormap.from_list(name, data)

    def _get_colormap(self, name: str) -> mcolors.LinearSegmentedColormap:
        """Get a colormap, loading it if necessary.

        Args:
            name: Name of the colormap

        Returns:
            LinearSegmentedColormap object

        """
        # Check if it's a reversed colormap
        is_reversed = name.endswith("_r")
        base_name = name[:-2] if is_reversed else name

        # Check if base colormap is available
        if base_name not in self.colormap_names:
            raise AttributeError(f"Colormap '{base_name}' not found")

        # Check cache first
        if name in self._cache:
            return self._cache[name]

        # Load base colormap if not in cache
        if base_name not in self._cache:
            data = self._load_colormap_data(base_name)
            self._cache[base_name] = self._create_colormap(base_name, data)

        # Create reversed version if needed
        if is_reversed:
            if name not in self._cache:
                base_cmap = self._cache[base_name]
                # Get the original data and reverse it
                data = self._load_colormap_data(base_name)
                reversed_data = data[::-1]
                self._cache[name] = self._create_colormap(name, reversed_data)

        return self._cache[name]

    def __getattr__(self, name: str) -> mcolors.LinearSegmentedColormap:
        """Dynamic attribute access for colormaps.

        Args:
            name: Name of the colormap

        Returns:
            LinearSegmentedColormap object

        """
        try:
            return self._get_colormap(name)
        except AttributeError:
            raise AttributeError(
                f"'{self.__class__.__name__}' object has no attribute '{name}'"
            )

    def __dir__(self) -> List[str]:
        """Return list of available attributes including colormaps."""
        attrs = list(self.__dict__.keys())
        attrs.extend(self.colormap_names)
        # Add reversed versions
        attrs.extend([f"{name}_r" for name in self.colormap_names])
        return sorted(set(attrs))

    @property
    def __all__(self) -> List[str]:
        """Return list of all available colormap names including reversed versions."""
        names = self.colormap_names.copy()
        names.extend([f"{name}_r" for name in self.colormap_names])
        return names


class Cmaps:
    """Unified colormap class providing access to all colormap collections.

    This class aggregates all colormap loaders and provides a single interface
    to access colormaps from GMT, SCM, cmocean, colorcet, and mintpy collections.
    """

    def __init__(self):
        """Initialize the unified colormap loader."""
        # Import the individual colormap classes
        from .cmocean.colormaps import _cmocean_colormaps
        from .colorcet.colormaps import _colorcet_colormaps
        from .GMT.colormaps import _gmt_colormaps
        from .mintpy.colormaps import _mintpy_colormaps
        from .SCM.colormaps import _scm_colormaps

        self._loaders = {
            "GMT": _gmt_colormaps,
            "SCM": _scm_colormaps,
            "cmocean": _cmocean_colormaps,
            "colorcet": _colorcet_colormaps,
            "mintpy": _mintpy_colormaps,
        }

        # Create a mapping of colormap names to their loaders
        self._colormap_map = {}
        for loader_name, loader in self._loaders.items():
            for cmap_name in loader.colormap_names:
                self._colormap_map[cmap_name] = loader
                self._colormap_map[f"{cmap_name}_r"] = loader

    @property
    def GMT(self):
        """Access GMT colormaps."""
        return self._loaders["GMT"]

    @property
    def SCM(self):
        """Access SCM colormaps."""
        return self._loaders["SCM"]

    @property
    def cmocean(self):
        """Access cmocean colormaps."""
        return self._loaders["cmocean"]

    @property
    def colorcet(self):
        """Access colorcet colormaps."""
        return self._loaders["colorcet"]

    @property
    def mintpy(self):
        """Access mintpy colormaps."""
        return self._loaders["mintpy"]

    def __getattr__(self, name: str) -> mcolors.LinearSegmentedColormap:
        """Dynamic attribute access for colormaps from any collection.

        Args:
            name: Name of the colormap

        Returns:
            LinearSegmentedColormap object

        """
        # Check if it's a reversed colormap
        is_reversed = name.endswith("_r")
        base_name = name[:-2] if is_reversed else name

        # Find the appropriate loader
        if base_name in self._colormap_map:
            loader = self._colormap_map[base_name]
            return getattr(loader, name)

        raise AttributeError(f"Colormap '{name}' not found in any collection")

    def __dir__(self) -> List[str]:
        """Return list of available attributes including all colormaps."""
        attrs = ["GMT", "SCM", "cmocean", "colorcet", "mintpy"]
        attrs.extend(self._colormap_map.keys())
        return sorted(set(attrs))

    @property
    def __all__(self) -> List[str]:
        """Return list of all available colormap names."""
        return sorted(self._colormap_map.keys())


# Create a global instance for direct access
cmaps = Cmaps()


# Import modules for backward compatibility
from . import GMT, SCM, cmocean, colorcet, mintpy


# Module-level attribute access for backward compatibility
def __getattr__(name: str):
    """Module-level dynamic attribute access for colormaps."""
    # First check if it's one of the custom colormaps defined in this module
    if name in [
        "GnBu_RdPl",
        "GnBu_RdPl_r",
        "RdGyBu",
        "RdGyBu_r",
        "WtBuPl",
        "WtBuPl_r",
        "WtBuGn",
        "WtBuGn_r",
        "WtRdPl",
        "WtRdPl_r",
        "WtHeatRed",
        "WtHeatRed_r",
    ]:
        return globals()[name]

    # Otherwise, try to get it from the unified cmaps instance
    try:
        return getattr(cmaps, name)
    except AttributeError:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Build __all__ dynamically from the cmaps instance and custom colormaps
__all__ = [
    "GnBu_RdPl",
    "GnBu_RdPl_r",
    "RdGyBu",
    "RdGyBu_r",
    "WtBuGn",
    "WtBuGn_r",
    "WtBuPl",
    "WtBuPl_r",
    "WtHeatRed",
    "WtHeatRed_r",
    "WtRdPl",
    "WtRdPl_r",
    "cmaps",
]
__all__.extend(cmaps.__all__)

names = cmaps.__all__.copy()

white = "0.95"

colors = [
    "#68011f",
    "#bb2832",
    "#e48066",
    "#fbccb4",
    "#ededed",
    "#c2ddec",
    "#6bacd1",
    "#2a71b2",
    "#0d3061",
]
RdGyBu = mcolors.LinearSegmentedColormap.from_list("RdGrBu", colors, N=100)
RdGyBu_r = mcolors.LinearSegmentedColormap.from_list("RdGrBu_r", colors[::-1], N=100)

colors = ["#8f07ff", "#d5734a", white, "#0571b0", "#01ef6c"]
GnBu_RdPl = mcolors.LinearSegmentedColormap.from_list("GnBu_RdPl", colors, N=100)
GnBu_RdPl_r = mcolors.LinearSegmentedColormap.from_list(
    "GnBu_RdPl_r",
    colors[::-1],
    N=100,
)

colors = [white, "#0571b0", "#8f07ff", "#d5734a"]
WtBuPl = mcolors.LinearSegmentedColormap.from_list("WtBuPl", colors, N=100)
WtBuPl_r = mcolors.LinearSegmentedColormap.from_list("WtBuPl_r", colors[::-1], N=100)

colors = [white, "#0571b0", "#01ef6c"]
WtBuGn = mcolors.LinearSegmentedColormap.from_list("WtBuGn", colors, N=100)
WtBuGn_r = mcolors.LinearSegmentedColormap.from_list("WtBuGn_r", colors[::-1], N=100)

colors = [white, "#d5734a", "#8f07ff"]
WtRdPl = mcolors.LinearSegmentedColormap.from_list("WtRdPl", colors, N=100)
WtRdPl_r = mcolors.LinearSegmentedColormap.from_list("WtRdPl_r", colors[::-1], N=100)

colors = [white, "#fff7b3", "#fb9d59", "#aa0526"]
WtHeatRed = mcolors.LinearSegmentedColormap.from_list("WtHeatRed", colors, N=100)
WtHeatRed_r = mcolors.LinearSegmentedColormap.from_list(
    "WtHeatRed_r",
    colors[::-1],
    N=100,
)


del colors
del white
