import matplotlib.colors as mcolors

# TODO: Add cpt-city colormaps
from . import GMT, SCM, cmocean, colorcet, mintpy
from .cmocean import (
    algae,
    algae_r,
    amp,
    amp_r,
    balance,
    balance_r,
    curl,
    curl_r,
    deep,
    deep_r,
    delta,
    delta_r,
    dense,
    dense_r,
    diff,
    diff_r,
    haline,
    haline_r,
    ice,
    ice_r,
    matter,
    matter_r,
    oxy,
    oxy_r,
    phase,
    phase_r,
    rain,
    rain_r,
    solar,
    solar_r,
    speed,
    speed_r,
    tarn,
    tarn_r,
    tempo,
    tempo_r,
    thermal,
    thermal_r,
    turbid,
    turbid_r,
)
from .colorcet import (
    bgy,
    bgy_r,
    bgyw,
    bgyw_r,
    bjy,
    bjy_r,
    bkr,
    bkr_r,
    bky,
    bky_r,
    blues,
    blues_r,
    bmw,
    bmw_r,
    bmy,
    bmy_r,
    bwy,
    bwy_r,
    colorwheel,
    colorwheel_r,
    coolwarm,
    coolwarm_r,
    cwr,
    cwr_r,
    dimgray,
    dimgray_r,
    fire,
    fire_r,
    gwv,
    gwv_r,
    isolum,
    isolum_r,
    kb,
    kb_r,
    kbc,
    kbc_r,
    kg,
    kg_r,
    kgy,
    kgy_r,
    kr,
    kr_r,
)
from .GMT import (
    abyss,
    abyss_r,
    bathy,
    bathy_r,
    cool,
    cool_r,
    copper,
    copper_r,
    cubhelix,
    cubhelix_r,
    cyclic,
    cyclic_r,
    dem1,
    dem1_r,
    dem2,
    dem2_r,
    dem3,
    dem3_r,
    dem4,
    dem4_r,
    drywet,
    drywet_r,
    earth,
    earth_r,
    elevation,
    elevation_r,
    etopo1,
    etopo1_r,
    geo,
    geo_r,
    globe,
    globe_r,
    gray,
    gray_r,
    haxby,
    haxby_r,
    hot,
    hot_r,
    inferno,
    inferno_r,
    jet,
    jet_r,
    magma,
    magma_r,
    nighttime,
    nighttime_r,
    no_green,
    no_green_r,
    ocean,
    ocean_r,
    plasma,
    plasma_r,
    polar,
    polar_r,
    rainbow,
    rainbow_r,
    red2green,
    red2green_r,
    relief,
    relief_r,
    seafloor,
    seafloor_r,
    sealand,
    sealand_r,
    seis,
    seis_r,
    split,
    split_r,
    srtm,
    srtm_r,
    terra,
    terra_r,
    topo,
    topo_r,
    turbo,
    turbo_r,
    viridis,
    viridis_r,
    world,
    world_r,
    wysiwyg,
    wysiwyg_r,
)
from .mintpy import cmy, cmy_r, dismph, dismph_r, romanian, romanian_r
from .SCM import (
    acton,
    acton_r,
    bam,
    bam_r,
    bamako,
    bamako_r,
    bamO,
    bamO_r,
    batlow,
    batlow_r,
    batlowK,
    batlowK_r,
    batlowW,
    batlowW_r,
    berlin,
    berlin_r,
    bilbao,
    bilbao_r,
    broc,
    broc_r,
    brocO,
    brocO_r,
    buda,
    buda_r,
    bukavu,
    bukavu_r,
    cork,
    cork_r,
    corkO,
    corkO_r,
    davos,
    davos_r,
    devon,
    devon_r,
    fes,
    fes_r,
    glasgow,
    glasgow_r,
    grayC,
    grayC_r,
    hawaii,
    hawaii_r,
    imola,
    imola_r,
    lajolla,
    lajolla_r,
    lapaz,
    lapaz_r,
    lipari,
    lipari_r,
    lisbon,
    lisbon_r,
    managua,
    managua_r,
    navia,
    navia_r,
    nuuk,
    nuuk_r,
    oleron,
    oleron_r,
    oslo,
    oslo_r,
    roma,
    roma_r,
    romaO,
    romaO_r,
    tofino,
    tofino_r,
    tokyo,
    tokyo_r,
    turku,
    turku_r,
    vanimo,
    vanimo_r,
    vik,
    vik_r,
    vikO,
    vikO_r,
)

__all__ = ["GnBu_RdPl", "RdGyBu", "WtBuPl", "WtHeatRed"]
__all__ += SCM.__all__
__all__ += GMT.__all__
__all__ += cmocean.__all__
__all__ += colorcet.__all__
__all__ += mintpy.__all__

names = __all__.copy()[3:]

white = "0.95"

colors = [
    "#68011f",
    "#bb2832",
    "#e48066",
    "#fbccb4",
    "#ededed",
    "#c2ddec",
    "#6bacd1",
    "#2a71b2",
    "#0d3061",
]
RdGyBu = mcolors.LinearSegmentedColormap.from_list("RdGrBu", colors, N=100)

colors = ["#8f07ff", "#d5734a", white, "#0571b0", "#01ef6c"]
GnBu_RdPl = mcolors.LinearSegmentedColormap.from_list("GnBu_RdPl", colors, N=100)
GnBu_RdPl_r = mcolors.LinearSegmentedColormap.from_list(
    "GnBu_RdPl_r",
    colors[::-1],
    N=100,
)

colors = [white, "#0571b0", "#8f07ff", "#d5734a"]
WtBuPl = mcolors.LinearSegmentedColormap.from_list("WtBuPl", colors, N=100)
WtBuPl_r = mcolors.LinearSegmentedColormap.from_list("WtBuPl_r", colors[::-1], N=100)

colors = [white, "#0571b0", "#01ef6c"]
WtBuGn = mcolors.LinearSegmentedColormap.from_list("WtBuGn", colors, N=100)
WtBuGn_r = mcolors.LinearSegmentedColormap.from_list("WtBuGn_r", colors[::-1], N=100)

colors = [white, "#d5734a", "#8f07ff"]
WtRdPl = mcolors.LinearSegmentedColormap.from_list("WtRdPl", colors, N=100)
WtRdPl_r = mcolors.LinearSegmentedColormap.from_list("WtRdPl_r", colors[::-1], N=100)

colors = [white, "#fff7b3", "#fb9d59", "#aa0526"]
WtHeatRed = mcolors.LinearSegmentedColormap.from_list("WtHeatRed", colors, N=100)
WtHeatRed_r = mcolors.LinearSegmentedColormap.from_list(
    "WtHeatRed_r",
    colors[::-1],
    N=100,
)


del mcolors
del colors
del white
