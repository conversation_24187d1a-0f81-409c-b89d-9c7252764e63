"""SCM Colormap Loader with dynamic loading capability."""

from pathlib import Path
from typing import List
import numpy as np

from .. import ColormapLoader


class SCMColormaps(ColormapLoader):
    """SCM colormap loader with dynamic loading."""
    
    def __init__(self):
        """Initialize SCM colormap loader."""
        super().__init__(Path(__file__).parent.absolute())
        self.version = "8.0.1"
        self._colormap_names = [
            "acton",
            "bam",
            "bamako",
            "bamO",
            "batlow",
            "batlowK",
            "batlowW",
            "berlin",
            "bilbao",
            "broc",
            "brocO",
            "buda",
            "bukavu",
            "cork",
            "corkO",
            "davos",
            "devon",
            "fes",
            "glasgow",
            "grayC",
            "hawaii",
            "imola",
            "lajolla",
            "lapaz",
            "lisbon",
            "lipari",
            "managua",
            "navia",
            "nuuk",
            "oleron",
            "oslo",
            "roma",
            "roma<PERSON>",
            "tofino",
            "tokyo",
            "turku",
            "vanimo",
            "vik",
            "vikO",
        ]
    
    @property
    def colormap_names(self) -> List[str]:
        """Return list of available SCM colormap names."""
        return self._colormap_names
    
    def _load_colormap_data(self, name: str) -> np.ndarray:
        """
        Load SCM colormap data from file.
        
        Args:
            name: Name of the colormap
            
        Returns:
            Numpy array containing colormap data
        """
        cmap_file = self.data_dir / name / f"{name}.txt"
        if not cmap_file.exists():
            raise FileNotFoundError(f"SCM colormap file not found: {cmap_file}")
        
        return np.loadtxt(cmap_file)


# Create a global instance
_scm_colormaps = SCMColormaps()

# Export all colormap names for backward compatibility
__all__ = _scm_colormaps.__all__

# Create module-level attributes for backward compatibility
def __getattr__(name: str):
    """Module-level attribute access for colormaps."""
    return getattr(_scm_colormaps, name)

def __dir__():
    """Return list of available module attributes."""
    return _scm_colormaps.__all__

# For convenience, also expose the names and version
names = _scm_colormaps.colormap_names
version = _scm_colormaps.version
