# FanInSAR Colormap Tests

This directory contains comprehensive pytest test suites for the refactored `faninsar.cmaps` module.

## Test Files

### `test_cmaps.py`
Main test suite covering core functionality:

- **TestColormapLoader**: Tests for the abstract base class
- **TestDynamicLoading**: Tests for lazy loading and caching mechanisms
- **TestUnifiedInterface**: Tests for the unified `Cmaps` interface
- **TestBackwardCompatibility**: Tests ensuring old code still works
- **TestErrorHandling**: Tests for proper error handling
- **TestReversedColormaps**: Tests for reversed colormap functionality
- **TestCaching**: Tests for caching behavior
- **TestCodeCompletion**: Tests for IDE code completion support
- **TestSpecificCollections**: Tests for each colormap collection (GMT, SCM, etc.)
- **TestPerformance**: Basic performance tests
- **TestLegacyColormaps**: Tests for backward compatibility with legacy interface

### `test_cmaps_advanced.py`
Advanced test suite covering edge cases and performance:

- **TestEdgeCases**: Tests for edge cases and invalid inputs
- **TestMemoryManagement**: Tests for memory usage and garbage collection
- **TestConcurrency**: Tests for concurrent access simulation
- **TestFileSystemInteraction**: Tests for file system error handling
- **TestPerformanceBenchmarks**: Detailed performance benchmarks
- **TestDataIntegrity**: Tests for colormap data validity
- **TestCompatibilityMatrix**: Tests for access pattern consistency

## Running Tests

### Run all colormap tests:
```bash
pytest tests/test_cmaps*.py -v
```

### Run specific test file:
```bash
pytest tests/test_cmaps.py -v
pytest tests/test_cmaps_advanced.py -v
```

### Run specific test class:
```bash
pytest tests/test_cmaps.py::TestDynamicLoading -v
```

### Run specific test method:
```bash
pytest tests/test_cmaps.py::TestDynamicLoading::test_lazy_loading -v
```

### Run with coverage:
```bash
pytest tests/test_cmaps*.py --cov=faninsar.cmaps --cov-report=html
```

## Test Coverage

The test suite provides comprehensive coverage of:

### ✅ **Core Functionality**
- Dynamic loading of colormaps
- Caching mechanisms
- Reversed colormap generation
- Error handling for invalid names
- All colormap collections (GMT, SCM, cmocean, colorcet, mintpy)

### ✅ **Interface Testing**
- Unified `cmaps` interface
- Collection-specific access (`cmaps.GMT.abyss`)
- Direct access (`cmaps.abyss`)
- Module-level access (`import faninsar.cmaps; cmaps.abyss`)
- Backward compatibility with old imports

### ✅ **Performance Testing**
- Import speed (should be fast due to lazy loading)
- First vs. cached access performance
- Memory usage scaling
- Cache efficiency

### ✅ **Edge Cases**
- Invalid colormap names
- Empty/None inputs
- Special characters in names
- File system errors
- Memory management

### ✅ **Code Completion**
- `__dir__` method functionality
- `__all__` property correctness
- IDE support verification

## Test Statistics

- **Total Tests**: 56
- **Main Test File**: 36 tests
- **Advanced Test File**: 20 tests
- **All Tests Pass**: ✅

## Key Features Tested

### 1. **Dynamic Loading**
```python
# First access loads from file
cmap = cmaps.GMT.abyss  # File I/O occurs here

# Second access uses cache
cmap2 = cmaps.GMT.abyss  # No file I/O, returns cached object
assert cmap is cmap2  # Same object
```

### 2. **Multiple Access Patterns**
```python
# All these return the same cached object
cmap1 = cmaps.GMT.abyss
cmap2 = cmaps.abyss
cmap3 = faninsar.cmaps.abyss
assert cmap1 is cmap2 is cmap3
```

### 3. **Reversed Colormaps**
```python
cmap = cmaps.GMT.abyss
cmap_r = cmaps.GMT.abyss_r
assert cmap is not cmap_r
assert cmap_r.name == "abyss_r"
```

### 4. **Error Handling**
```python
with pytest.raises(AttributeError):
    _ = cmaps.nonexistent_colormap
```

### 5. **Code Completion**
```python
# These should show up in IDE autocomplete
attrs = dir(cmaps.GMT)
assert "abyss" in attrs
assert "abyss_r" in attrs
```

## Performance Benchmarks

The tests verify that:

- **Import time** < 1 second (lazy loading)
- **First access** < 0.1 seconds per colormap
- **Cached access** < 0.01 seconds for 1000 accesses
- **Memory usage** scales reasonably with cache size

## Backward Compatibility

All tests ensure that existing code continues to work:

```python
# Old style - still works
from faninsar.cmaps import GMT, SCM
cmap1 = GMT.abyss
cmap2 = SCM.acton

# New style - also works
from faninsar.cmaps import cmaps
cmap3 = cmaps.GMT.abyss
cmap4 = cmaps.abyss

# All return the same cached objects
assert cmap1 is cmap3
```

## Contributing

When adding new colormap functionality:

1. Add tests to the appropriate test class
2. Ensure all existing tests still pass
3. Add performance tests if needed
4. Update this README if new test categories are added

## Dependencies

The tests require:
- `pytest`
- `matplotlib`
- `numpy`
- `faninsar` (the package being tested)

Optional for advanced testing:
- `pytest-cov` (for coverage reports)
- `pytest-xdist` (for parallel test execution)
