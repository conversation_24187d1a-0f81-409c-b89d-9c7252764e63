#!/usr/bin/env python3
"""
Test script to verify the refactored colormap dynamic loading functionality.

This script tests:
1. Dynamic loading of colormaps from all collections
2. Backward compatibility with old import style
3. Performance of lazy loading vs eager loading
4. Access through the unified cmaps interface
"""

import time
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """Test that basic imports still work."""
    print("Testing basic imports...")
    
    try:
        import faninsar.cmaps as cmaps
        print("✓ Main cmaps module imported successfully")
        
        # Test that the unified cmaps instance exists
        assert hasattr(cmaps, 'cmaps'), "cmaps instance not found"
        print("✓ Unified cmaps instance exists")
        
        # Test that individual modules are accessible
        assert hasattr(cmaps, 'GMT'), "GMT module not accessible"
        assert hasattr(cmaps, 'SCM'), "SCM module not accessible"
        assert hasattr(cmaps, 'cmocean'), "cmocean module not accessible"
        assert hasattr(cmaps, 'colorcet'), "colorcet module not accessible"
        assert hasattr(cmaps, 'mintpy'), "mintpy module not accessible"
        print("✓ All submodules are accessible")
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False
    
    return True


def test_dynamic_loading():
    """Test dynamic loading of colormaps."""
    print("\nTesting dynamic loading...")
    
    try:
        import faninsar.cmaps as cmaps
        
        # Test GMT colormap
        gmt_cmap = cmaps.abyss
        assert gmt_cmap is not None, "GMT colormap not loaded"
        print("✓ GMT colormap loaded dynamically")
        
        # Test SCM colormap
        scm_cmap = cmaps.acton
        assert scm_cmap is not None, "SCM colormap not loaded"
        print("✓ SCM colormap loaded dynamically")
        
        # Test cmocean colormap
        cmocean_cmap = cmaps.algae
        assert cmocean_cmap is not None, "cmocean colormap not loaded"
        print("✓ cmocean colormap loaded dynamically")
        
        # Test colorcet colormap
        colorcet_cmap = cmaps.bkr
        assert colorcet_cmap is not None, "colorcet colormap not loaded"
        print("✓ colorcet colormap loaded dynamically")
        
        # Test mintpy colormap
        mintpy_cmap = cmaps.cmy
        assert mintpy_cmap is not None, "mintpy colormap not loaded"
        print("✓ mintpy colormap loaded dynamically")
        
        # Test reversed colormaps
        reversed_cmap = cmaps.abyss_r
        assert reversed_cmap is not None, "Reversed colormap not loaded"
        print("✓ Reversed colormap loaded dynamically")
        
    except Exception as e:
        print(f"✗ Dynamic loading test failed: {e}")
        return False
    
    return True


def test_unified_cmaps_interface():
    """Test the unified cmaps interface."""
    print("\nTesting unified cmaps interface...")
    
    try:
        import faninsar.cmaps as cmaps
        
        # Test accessing through cmaps instance
        gmt_cmap = cmaps.cmaps.GMT.abyss
        assert gmt_cmap is not None, "GMT colormap not accessible through cmaps.GMT"
        print("✓ GMT colormap accessible through cmaps.GMT")
        
        scm_cmap = cmaps.cmaps.SCM.acton
        assert scm_cmap is not None, "SCM colormap not accessible through cmaps.SCM"
        print("✓ SCM colormap accessible through cmaps.SCM")
        
        # Test direct access through cmaps
        direct_cmap = cmaps.cmaps.abyss
        assert direct_cmap is not None, "Colormap not accessible directly through cmaps"
        print("✓ Colormap accessible directly through cmaps")
        
        # Test that both methods return the same colormap
        assert gmt_cmap is direct_cmap, "Different instances returned for same colormap"
        print("✓ Same instance returned for same colormap")
        
    except Exception as e:
        print(f"✗ Unified cmaps interface test failed: {e}")
        return False
    
    return True


def test_performance():
    """Test performance of dynamic loading."""
    print("\nTesting performance...")
    
    try:
        import faninsar.cmaps as cmaps
        
        # Test first access (should load from file)
        start_time = time.time()
        cmap1 = cmaps.abyss
        first_access_time = time.time() - start_time
        
        # Test second access (should use cache)
        start_time = time.time()
        cmap2 = cmaps.abyss
        second_access_time = time.time() - start_time
        
        # Verify it's the same object (cached)
        assert cmap1 is cmap2, "Different objects returned - caching not working"
        print("✓ Caching is working correctly")
        
        # Second access should be much faster
        if second_access_time < first_access_time:
            print(f"✓ Performance improvement: {first_access_time:.4f}s -> {second_access_time:.4f}s")
        else:
            print(f"⚠ No significant performance improvement: {first_access_time:.4f}s -> {second_access_time:.4f}s")
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False
    
    return True


def test_error_handling():
    """Test error handling for non-existent colormaps."""
    print("\nTesting error handling...")
    
    try:
        import faninsar.cmaps as cmaps
        
        # Test accessing non-existent colormap
        try:
            _ = cmaps.nonexistent_colormap
            print("✗ Should have raised AttributeError for non-existent colormap")
            return False
        except AttributeError:
            print("✓ Correctly raised AttributeError for non-existent colormap")
        
        # Test accessing non-existent colormap through cmaps
        try:
            _ = cmaps.cmaps.nonexistent_colormap
            print("✗ Should have raised AttributeError for non-existent colormap through cmaps")
            return False
        except AttributeError:
            print("✓ Correctly raised AttributeError for non-existent colormap through cmaps")
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False
    
    return True


def test_custom_colormaps():
    """Test that custom colormaps still work."""
    print("\nTesting custom colormaps...")
    
    try:
        import faninsar.cmaps as cmaps
        
        # Test custom colormaps defined in the main module
        custom_cmaps = ['GnBu_RdPl', 'RdGyBu', 'WtBuPl', 'WtHeatRed']
        
        for cmap_name in custom_cmaps:
            cmap = getattr(cmaps, cmap_name)
            assert cmap is not None, f"Custom colormap {cmap_name} not found"
            
            # Test reversed version
            cmap_r = getattr(cmaps, f"{cmap_name}_r")
            assert cmap_r is not None, f"Custom colormap {cmap_name}_r not found"
        
        print("✓ All custom colormaps accessible")
        
    except Exception as e:
        print(f"✗ Custom colormaps test failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("Testing refactored colormap dynamic loading functionality")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_dynamic_loading,
        test_unified_cmaps_interface,
        test_performance,
        test_error_handling,
        test_custom_colormaps,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactoring was successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
