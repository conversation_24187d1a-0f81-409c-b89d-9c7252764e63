#!/usr/bin/env python3
"""
Demonstration of the enhanced colormap functionality.

This script shows how to use the new EnhancedLinearSegmentedColormap
features integrated into the faninsar.cmaps system.
"""

import sys
from pathlib import Path
import tempfile

import numpy as np
import matplotlib.pyplot as plt

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from faninsar.cmaps import cmaps
from faninsar.cmaps.enhanced_colormap import EnhancedLinearSegmentedColormap


def demo_enhanced_features():
    """Demonstrate enhanced colormap features."""
    print("=== Enhanced Colormap Features Demo ===")
    print()
    
    # Get an enhanced colormap from the cmaps system
    cmap = cmaps.GMT.abyss
    print(f"Colormap type: {type(cmap).__name__}")
    print(f"Colormap name: {cmap.name}")
    print(f"Is enhanced: {isinstance(cmap, EnhancedLinearSegmentedColormap)}")
    print()
    
    # Demonstrate enhanced methods
    print("1. RGB Array Extraction:")
    rgb_array = cmap.to_rgb_array(N=10)
    print(f"   RGB array shape: {rgb_array.shape}")
    print(f"   First color (RGB): {rgb_array[0]}")
    print(f"   Last color (RGB): {rgb_array[-1]}")
    print()
    
    print("2. Dictionary Conversion:")
    cmap_dict = cmap.to_dict(N=5)
    print(f"   Dictionary keys: {list(cmap_dict.keys())}")
    print(f"   Red channel entries: {len(cmap_dict['red'])}")
    print(f"   First red entry: {cmap_dict['red'][0]}")
    print()
    
    print("3. Lightening:")
    lightened = cmap.lighten(0.5)
    print(f"   Lightened colormap name: {lightened.name}")
    print(f"   Lightened colormap type: {type(lightened).__name__}")
    
    # Compare alpha values
    original_colors = cmap(np.linspace(0, 1, 3))
    lightened_colors = lightened(np.linspace(0, 1, 3))
    print(f"   Original alpha: {original_colors[0, 3]}")
    print(f"   Lightened alpha: {lightened_colors[0, 3]}")
    print()
    
    print("4. Cropping:")
    cropped = cmap.crop(vmin=-3, vmax=7, pivot=0, N=128)
    print(f"   Cropped colormap name: {cropped.name}")
    print(f"   Cropped colormap N: {cropped.N}")
    print()
    
    print("5. Percentage Cropping:")
    cropped_percent = cmap.crop_by_percent(20, which="both")
    print(f"   Percent cropped name: {cropped_percent.name}")
    print(f"   Percent cropped type: {type(cropped_percent).__name__}")
    print()


def demo_creation_methods():
    """Demonstrate enhanced colormap creation methods."""
    print("=== Enhanced Creation Methods ===")
    print()
    
    print("1. From RGB Array:")
    rgb_data = np.array([
        [255, 0, 0],    # Red
        [255, 255, 0],  # Yellow
        [0, 255, 0],    # Green
        [0, 255, 255],  # Cyan
        [0, 0, 255],    # Blue
    ])
    rgb_cmap = EnhancedLinearSegmentedColormap.from_rgb_array("demo_rgb", rgb_data)
    print(f"   Created from RGB array: {rgb_cmap.name}")
    print(f"   Type: {type(rgb_cmap).__name__}")
    print()
    
    print("2. From Hex Colors:")
    hex_colors = ["#FF0000", "#FFFF00", "#00FF00", "#00FFFF", "#0000FF"]
    hex_cmap = EnhancedLinearSegmentedColormap.from_hex_colors("demo_hex", hex_colors)
    print(f"   Created from hex colors: {hex_cmap.name}")
    print(f"   Type: {type(hex_cmap).__name__}")
    print()
    
    print("3. From Color List:")
    color_list = ["red", "yellow", "green", "cyan", "blue"]
    list_cmap = EnhancedLinearSegmentedColormap.from_list("demo_list", color_list)
    print(f"   Created from color list: {list_cmap.name}")
    print(f"   Type: {type(list_cmap).__name__}")
    print()


def demo_file_operations():
    """Demonstrate file operations."""
    print("=== File Operations ===")
    print()
    
    cmap = cmaps.SCM.acton
    
    print("1. Save RGB Data:")
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp:
        tmp_path = Path(tmp.name)
    
    try:
        cmap.save_rgb(tmp_path, N=20)
        print(f"   Saved RGB data to: {tmp_path}")
        
        # Read back and verify
        loaded_data = np.loadtxt(tmp_path)
        print(f"   Loaded data shape: {loaded_data.shape}")
        print(f"   Data range: [{loaded_data.min():.3f}, {loaded_data.max():.3f}]")
        
    finally:
        tmp_path.unlink(missing_ok=True)
    
    print()


def demo_backward_compatibility():
    """Demonstrate backward compatibility."""
    print("=== Backward Compatibility ===")
    print()
    
    # All existing functionality should still work
    cmap = cmaps.GMT.relief
    
    print("1. Standard matplotlib functionality:")
    colors = cmap(np.linspace(0, 1, 5))
    print(f"   Generated colors shape: {colors.shape}")
    print(f"   Color range: [{colors.min():.3f}, {colors.max():.3f}]")
    print()
    
    print("2. Standard attributes:")
    print(f"   Name: {cmap.name}")
    print(f"   N: {cmap.N}")
    print(f"   Has set_bad: {hasattr(cmap, 'set_bad')}")
    print(f"   Has set_over: {hasattr(cmap, 'set_over')}")
    print(f"   Has set_under: {hasattr(cmap, 'set_under')}")
    print()
    
    print("3. Enhanced methods available:")
    enhanced_methods = [
        "to_rgb_array", "save_rgb", "to_dict", 
        "lighten", "crop", "crop_by_percent"
    ]
    for method in enhanced_methods:
        has_method = hasattr(cmap, method)
        print(f"   Has {method}: {has_method}")
    print()


def demo_all_collections():
    """Demonstrate that all collections return enhanced colormaps."""
    print("=== All Collections Enhanced ===")
    print()
    
    collections = [
        ("GMT", cmaps.GMT.abyss),
        ("SCM", cmaps.SCM.acton),
        ("cmocean", cmaps.cmocean.algae),
        ("colorcet", cmaps.colorcet.bkr),
        ("mintpy", cmaps.mintpy.cmy),
    ]
    
    for collection_name, cmap in collections:
        is_enhanced = isinstance(cmap, EnhancedLinearSegmentedColormap)
        has_tools = hasattr(cmap, "to_rgb_array")
        print(f"{collection_name:>8}: Enhanced={is_enhanced}, Tools={has_tools}, Name={cmap.name}")
    
    print()
    
    # Test custom colormaps too
    import faninsar.cmaps as cmaps_module
    custom_cmaps = [
        ("GnBu_RdPl", cmaps_module.GnBu_RdPl),
        ("RdGyBu", cmaps_module.RdGyBu),
        ("WtBuPl", cmaps_module.WtBuPl),
        ("WtHeatRed", cmaps_module.WtHeatRed),
    ]
    
    print("Custom colormaps:")
    for cmap_name, cmap in custom_cmaps:
        is_enhanced = isinstance(cmap, EnhancedLinearSegmentedColormap)
        has_tools = hasattr(cmap, "to_rgb_array")
        print(f"{cmap_name:>10}: Enhanced={is_enhanced}, Tools={has_tools}")
    
    print()


def demo_practical_example():
    """Demonstrate a practical example."""
    print("=== Practical Example ===")
    print()
    
    # Get a colormap and modify it for specific use case
    base_cmap = cmaps.GMT.earth
    print(f"Base colormap: {base_cmap.name}")
    
    # Create a lightened version for overlay
    overlay_cmap = base_cmap.lighten(0.6)
    print(f"Created overlay version: {overlay_cmap.name}")
    
    # Crop for specific data range (e.g., bathymetry/topography)
    topo_cmap = base_cmap.crop(vmin=-2000, vmax=3000, pivot=0)
    print(f"Created topography version: {topo_cmap.name}")
    
    # Extract RGB for external use
    rgb_data = topo_cmap.to_rgb_array(N=256)
    print(f"Extracted RGB data: {rgb_data.shape}")
    
    # Convert to dictionary for custom matplotlib colormap
    cmap_dict = topo_cmap.to_dict(N=100)
    print(f"Created dictionary with {len(cmap_dict['red'])} color points")
    
    print()
    print("This workflow demonstrates how the enhanced colormaps")
    print("can be easily modified and exported for various use cases!")
    print()


def main():
    """Run all demonstrations."""
    print("FanInSAR Enhanced Colormap Demonstration")
    print("=" * 50)
    print()
    
    demo_enhanced_features()
    demo_creation_methods()
    demo_file_operations()
    demo_backward_compatibility()
    demo_all_collections()
    demo_practical_example()
    
    print("=" * 50)
    print("Demonstration complete!")
    print()
    print("Key benefits of the enhanced colormap system:")
    print("• All existing code continues to work unchanged")
    print("• New tools methods available on all colormaps")
    print("• Easy colormap manipulation (crop, lighten, etc.)")
    print("• Multiple creation methods (RGB, hex, list)")
    print("• Export capabilities (RGB arrays, dictionaries)")
    print("• Maintains matplotlib compatibility")


if __name__ == "__main__":
    main()
